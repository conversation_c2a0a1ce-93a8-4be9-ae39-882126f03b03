import { env } from "$env/dynamic/private";
import { type Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { useQuota } from "$lib/firebase/firebase-admin";
import { chatCompletions } from "$lib/services/server.service";
import { QuotaType } from '$lib/types/activation-code.types';

export const POST: RequestHandler = async ({ request, url }) => {
    let controller = new AbortController();

    // 获取 Fetch API 的 AbortSignal
    const signal = request.signal;

    // 监听客户端中断请求
    signal.addEventListener('abort', () => {
        console.log('Client aborted the request.');
        controller.abort(); // 如果你有其他异步任务，可以中止
    });

    if (!env.GEMINI_API_KEY) {
        return new Response(JSON.stringify({
            success: false,
            error: "GEMINI_API_KEY not set"
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    const requestBody = await request.json();

    const token = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

    if (!token || token === "") {
        return new Response(JSON.stringify({ success: false, error: "Unauthorized (102401)" }), { status: 401 });
    }

    console.log('request', requestBody.model, url.pathname + url.search);

    try {
        const response = await useQuota(token, QuotaType.COMPLETION, async () => {
            return await chatCompletions(controller.signal, requestBody);
        });
        return response;
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        }
        return new Response(JSON.stringify({
            success: false,
            error: err
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}