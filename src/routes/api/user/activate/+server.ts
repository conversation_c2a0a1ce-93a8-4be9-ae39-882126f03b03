import { error, json, type RequestHandler } from "@sveltejs/kit";
import { initializeFirebaseAdmin, updateUserActivationCode, validUserToken } from "$lib/firebase/firebase-admin";

// 激活码激活, 无效激活码无法注册成功, 用户即使登录成功, 若无激活码, 也不能使用相关功能
export const POST: RequestHandler = async ({ request, url }) => {
    const { email, activationCode } = await request.json();

    const userToken = request.headers.get("Authorization")?.replace("Bearer ", "").trim();

    if (!userToken || userToken === "") {
        return error(401, "Unauthorized");
    }

    let controller = new AbortController();

    // 获取 Fetch API 的 AbortSignal
    const signal = request.signal;

    // 监听客户端中断请求
    signal.addEventListener('abort', () => {
        console.log('Client aborted the request.');
        controller.abort(); // 如果你有其他异步任务，可以中止
    });

    const time1 = new Date();

    console.log('request', url.pathname + url.search, email);
    console.log(time1, 0);

    try {
        initializeFirebaseAdmin();

        const isValidUser = validUserToken(email, userToken);

        if (!isValidUser) {
            return error(401, "Invalid user");
        }

        // 激活成功并保存
        const { success, err } = await updateUserActivationCode(email, userToken, activationCode);

        console.log(new Date(), new Date().getTime() - time1.getTime());
        if (!success) {
            return error(500, err || "Update user activation code failed");
        }
        return new Response(JSON.stringify({
            success: true,
        }), {
            status: 204, headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        }
        return new Response(JSON.stringify({
            success: false,
            error: `Activate error: ${err}`
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}