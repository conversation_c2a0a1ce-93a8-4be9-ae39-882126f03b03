import { userAuth } from "$lib/stores/app.store";
import { get } from "svelte/store";
import { type IAuthStrategy, type AuthResponse, type AuthUser, type AuthToast, AuthMethod } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";
import { browser } from "$app/environment";
import { fireapp } from "$lib/firebase/firebase";
import {
  createUserWithEmailAndPassword, getIdToken, onAuthStateChanged,
  sendEmailVerification as firebaseSendEmailVerification,
  sendPasswordResetEmail as firebaseSendPasswordResetEmail,
  signOut as firebaseSignOut,
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithPopup,
  type ActionCodeSettings,
  sendSignInLinkToEmail,
  confirmPasswordReset,
  signInWithEmailLink,
  applyActionCode,
  type User,
} from "firebase/auth";
import { InputValidator } from "$lib/utils/security";
import { env } from "$env/dynamic/public";

export class FirebaseSDKAuthStrategy implements IAuthStrategy {

  private unsubscribeAuth: (() => void) | null = null;

  readonly strategyType = AuthStrategy.FIREBASE_SDK;

  constructor() {
    if (browser) {
      this.initializeAuth();
    }
  }

  supportAuthMethod(method: AuthMethod): boolean {
    return [
      AuthMethod.PASSWORD,
      AuthMethod.GOOGLE,
      AuthMethod.EMAIL_LINK,
    ].some(m => m === method);
  }

  private async initializeAuth() {
    try {
      const { auth } = fireapp();

      // 监听认证状态变化
      this.unsubscribeAuth = onAuthStateChanged(auth, async (firebaseUser) => {
        console.log("User signed in:", firebaseUser);
        if (firebaseUser) {
          const idToken = await firebaseUser.getIdTokenResult(true);
          const expirationTime = new Date(idToken.expirationTime).getTime();
          const quotaPool = idToken.claims.quota_pool;
          this._updateUser({
            ...firebaseUser,
            // idToken,
            expirationTime,
            quotaPool,
            localId: firebaseUser.uid,
            registered: true
          });
        }
      });
    } catch (error) {
      console.error("Firebase SDK initialization failed:", error);
    }
  }

  private _updateUser(firebaseUserData: any): AuthUser {
    userAuth.update((user: AuthUser) => {
      console.log("Update user(firebase sdk):", user, firebaseUserData);
      return {
        ...user,
        ...firebaseUserData,
      };
    });

    return get(userAuth);
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }

    const passwordValidation = InputValidator.validatePassword(password);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);
      const { auth } = fireapp();

      const userCredential = await createUserWithEmailAndPassword(auth, cleanEmail, password);
      const idToken = await getIdToken(userCredential.user, true);
      const user = this._updateUser({
        ...userCredential.user,
        idToken,
        localId: userCredential.user.uid,
        registered: true
      });

      // 发送验证邮件
      await firebaseSendEmailVerification(userCredential.user, this._actionCodeSettings());

      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "注册成功" });
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }

    const passwordValidation = InputValidator.validatePassword(password);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);
      const { auth } = fireapp();

      const userCredential = await signInWithEmailAndPassword(auth, cleanEmail, password);
      const idToken = await getIdToken(userCredential.user, true);
      const user = this._updateUser({
        ...userCredential.user,
        idToken,
        registered: true
      });

      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "登录成功" });
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      const provider = new GoogleAuthProvider();
      provider.addScope("profile");
      provider.addScope("email");
      provider.setCustomParameters({ prompt: "select_account" });

      const result = await signInWithPopup(auth, provider);
      const idToken = await getIdToken(result.user, true);
      const user = this._updateUser({
        ...result.user,
        idToken,
        registered: true
      });

      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "Google 登录成功" });
    } catch (error: any) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async signOut(): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      await firebaseSignOut(auth);
      userAuth.set({} as AuthUser);
      console.log("Signed out, redirecting to login");
      if (browser) {
        return { success: true, toast: { code: "SUCCESS", message: "已退出登录" }, redirectUrl: "/login" };
      }
      return { success: true, toast: { code: "SUCCESS", message: "已退出登录" } };
    } catch (error) {
      console.log("Sign out failed:", error);
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  private _actionCodeSettings(): ActionCodeSettings {
    // 配置邮件链接设置
    const actionCodeSettings: ActionCodeSettings = {
      // 用户点击邮件链接后的重定向 URL
      url: `${window.location.origin}${env.PUBLIC_GOOGLE_FIREBASE_AUTH_CONTINUE_URL}`,
      // 这必须为 true
      handleCodeInApp: true,
    };
    return actionCodeSettings;
  }

  async sendEmailVerification(idToken?: string): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      if (auth.currentUser) {
        await firebaseSendEmailVerification(auth.currentUser, this._actionCodeSettings());
        return { success: true };
      }
      return { success: false, toast: { code: "NO_USER", message: "用户不存在" } };
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async sendPasswordResetEmail(email: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }
    try {
      const { auth } = fireapp();
      await firebaseSendPasswordResetEmail(auth, email, this._actionCodeSettings());
      return { success: true };
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async sendEmailLinkSignIn(email: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }
    try {
      const { auth } = fireapp();
      await sendSignInLinkToEmail(auth, email.trim(), this._actionCodeSettings());
      return { success: true };
    } catch (error: any) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async handleAuthCallback(mode: string, oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    if (!oobCode || oobCode.trim().length == 0) {
      return { success: false, toast: { code: "INVALID_OOB_CODE", message: "无效的认证链接(oob)" }, redirectUrl: continueUrl };
    }
    // Firebase SDK会自动处理大部分回调，这里主要处理密码重置
    switch (mode) {
      case "resetPassword":
        if (continueUrl) {
          return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}&continueUrl=${encodeURIComponent(continueUrl)}` };
        } else {
          return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}` };
        }
      case "signIn":
        return await this.handleEmailLinkSignIn(oobCode, continueUrl);
      case "verifyEmail":
        return await this.handleEmailVerification(oobCode, continueUrl);
      default:
        return { success: false, toast: { code: "INVALID_MODE", message: "无效的操作链接(mode)" }, redirectUrl: continueUrl };
    }
  }

  async _handleOobcodeSuccess(firebaseUser: User | null, continueUrl?: string): Promise<AuthResponse> {
    if (firebaseUser) {
      const idToken = await getIdToken(firebaseUser, true);
      const user = this._updateUser({
        ...firebaseUser,
        idToken,
        emailVerified: true
      });
      return this._checkVerificationAndActivation(user, { code: "SUCCESS", message: "登录成功" }, continueUrl);
    } else {
      // 邮箱验证成功, 但用户未登录
      return { success: true, toast: { code: "NO_USER", message: "验证成功, 请登录" }, redirectUrl: '/login' };
    }
  }

  async handleEmailLinkSignIn(oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    try {
      let emailForSignIn = this.getCurrentUser()?.email;
      if (!emailForSignIn) {
        emailForSignIn = window.prompt("请输入您的邮箱地址以完成登录:") || "";
      }
      if (!emailForSignIn) {
        return { success: false, toast: { code: "MISSING_EMAIL", message: "需要邮箱地址才能完成登录" } };
      }

      const { auth } = fireapp();
      // Get user info
      const userCredential = await signInWithEmailLink(auth, oobCode);
      return this._handleOobcodeSuccess(userCredential.user, continueUrl);
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async handleEmailVerification(oobCode: string, continueUrl?: string): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      await applyActionCode(auth, oobCode);
      return this._handleOobcodeSuccess(auth.currentUser, continueUrl);
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async handlePasswordReset(oobCode: string, newPassword: string, continueUrl?: string): Promise<AuthResponse> {
    const passwordValidation = InputValidator.validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const { auth } = fireapp();
      // 重置密码需要先确认邮箱，否则会报错
      await confirmPasswordReset(auth, oobCode, newPassword);
      return { success: true, toast: { code: "SUCCESS", message: "密码重置成功, 请重新登录！" }, redirectUrl: "/login" };
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async activateUser(code: string): Promise<AuthResponse> {
    try {
      const currentUser = this.getCurrentUser();
      if (!currentUser) {
        return { success: false, toast: { code: "NO_USER", message: "请先登录" } };
      }

      const cleanCode = InputValidator.sanitizeString(code);
      const idToken = await this.idToken();

      const response = await fetch("/api/user/activate", {
        method: "POST",
        headers: { "Content-Type": "application/json", "Authorization": `Bearer ${idToken}` },
        body: JSON.stringify({ email: currentUser.email, activationCode: cleanCode }),
      });

      if (response.ok) {
        return { success: true, toast: { code: "ACTIVATION_SUCCESS", message: "激活成功！欢迎使用蘑菇AI小说平台" } };
      }

      const data = await response.json();
      return { success: false, toast: { code: "ACTIVATION_FAILED", message: data.error || "激活码验证失败" } };
    } catch (error: any) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  async idToken(): Promise<string> {
    try {
      const { auth } = fireapp();
      if (auth.currentUser) {
        const idToken = await getIdToken(auth.currentUser, true);
        this._updateUser({ idToken })
        return idToken;
      }
      return "";
    } catch (error) {
      return "";
    }
  }

  async reload(): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      if (auth.currentUser) {
        await auth.currentUser.reload();
        const user = this._updateUser(auth.currentUser);
        return { success: true, user };
      }
      return { success: false };
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  getCurrentUser(): AuthUser | null {
    const user = get(userAuth);
    if (!user || !user.email) {
      const { auth } = fireapp();
      if (auth.currentUser) {
        const user = this._updateUser(auth.currentUser);
        return user;
      }
    }
    return user && user.idToken ? user : null;
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()?.idToken;
  }
  isEmailVerified(): boolean {
    return !!this.getCurrentUser()?.emailVerified;
  }
  isActivated(): boolean {
    return !!this.getCurrentUser()?.quotaPool;
  }

  async isAvailable(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);

      // 尝试访问Firebase的公开端点
      const response = await fetch('https://firebase.googleapis.com/v1beta1/projects', {
        method: 'GET',
        signal: controller.signal,
        mode: 'no-cors' // 避免CORS问题
      });

      clearTimeout(timeoutId);
      // 即使返回错误，只要能连接到代理服务就说明可用
      return true;
    } catch (error) {
      console.log('Firebase not available:', error);
      return false;
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; latency?: number; }> {
    const startTime = Date.now();
    try {
      const available = await this.isAvailable();
      const latency = Date.now() - startTime;

      if (available) {
        // 额外检查网络连接
        return { healthy: true, latency };
      }

      return { healthy: false };
    } catch (error) {
      return { healthy: false };
    }
  }

  private _checkVerificationAndActivation(user: AuthUser, toast: AuthToast, continueUrl?: string): AuthResponse {
    if (!this.isEmailVerified()) {
      return { success: true, user, toast: { code: "EMAIL_NOT_VERIFIED", message: `${toast.message}: "邮箱未验证"` }, redirectUrl: "/auth/verify-email" };
    }
    if (!this.isActivated()) {
      return { success: true, user, toast: { code: "ACCOUNT_NOT_ACTIVATED", message: `${toast.message}: "账号未激活` }, redirectUrl: "/auth/activate" };
    }
    return { success: true, user, toast, redirectUrl: continueUrl };
  }

  private parseFirebaseError(error: any): AuthToast {
    console.warn('auth-strategy-firebase.service parseFirebaseError:', error);

    const errorCode = error?.code || error?.message || "UNKNOWN_ERROR";
    const errorMessages: { [key: string]: string } = {
      "auth/email-already-in-use": "该邮箱已被注册",
      "auth/operation-not-allowed": "该操作不被允许",
      "auth/too-many-requests": "尝试次数过多，请稍后再试",
      "auth/user-not-found": "邮箱不存在",
      "auth/wrong-password": "密码错误",
      "auth/user-disabled": "用户账号已被禁用",
      "auth/invalid-email": "邮箱格式无效",
      "auth/weak-password": "密码强度不够",
      "auth/missing-password": "请输入密码",
      "auth/invalid-credential": "登录凭据无效",
      "auth/credential-already-in-use": "该凭据已被其他账号使用",
      "auth/popup-closed-by-user": "登录窗口被用户关闭",
      "auth/popup-blocked": "登录弹窗被浏览器阻止",
      "auth/network-request-failed": "网络请求失败，请检查网络连接",
    };

    return {
      code: errorCode,
      message: errorMessages[errorCode] || `认证失败: ${errorCode}`
    };
  }
  // 清理资源
  destroy() {
    if (this.unsubscribeAuth) {
      this.unsubscribeAuth();
      this.unsubscribeAuth = null;
    }
  }
}
