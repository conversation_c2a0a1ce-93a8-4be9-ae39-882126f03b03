// 认证策略类型定义
import { ActivationCodeType } from "../types/activation-code.types";

export interface UserQuote {
  id: string;
  type: ActivationCodeType;
  amount: {
    agent: number;
    completion: number;
  };
  used: {
    agent: number;
    completion: number;
  };
  created_at: string;
}

export interface AuthUser {
  uid: string;
  email: string;
  displayName?: string;
  emailVerified: boolean;
  photoURL?: string;
  phoneNumber?: string;
  createdAt?: string;
  lastLoginAt?: string;
  isAnonymous?: boolean;
  idToken: string;
  refreshToken: string;
  expiresIn?: number;
  expirationTime?: number;
  registered?: boolean;
  providerData?: any[];
  quotaPool?: UserQuote[];
  [key: string]: any;
}

export interface AuthToast {
  code: string;
  message: string;
}

export interface AuthResponse {
  success: boolean;
  user?: AuthUser;
  toast?: AuthToast;
  redirectUrl?: string;
}

export interface NetworkStatus {
  isOnline: boolean;
  canAccessFirebase: boolean;
  canAccessGoogle: boolean;
  lastChecked: number;
}

export const AuthStrategy = {
  FIREBASE_SDK: 'firebase_sdk' as const,
  SERVER_PROXY: 'server_proxy' as const,
  HYBRID: 'hybrid' as const, // 自动选择最佳策略
} as const;

export type AuthStrategy = typeof AuthStrategy[keyof typeof AuthStrategy];

export interface AuthStrategyConfig {
  strategy: AuthStrategy;
  fallbackStrategy?: AuthStrategy;
  autoSwitch: boolean;
  checkInterval: number; // 网络检测间隔（毫秒）
  timeout: number; // 网络检测超时（毫秒）
}

export const AuthMethod = {
  PASSWORD: 'password' as const,
  GOOGLE: 'google' as const,
  EMAIL_LINK: 'emailLink' as const,
} as const;

export type AuthMethod = typeof AuthMethod[keyof typeof AuthMethod];

// 认证策略接口
export interface IAuthStrategy {
  readonly strategyType: AuthStrategy;

  // 支持的认证方式
  supportAuthMethod(method: AuthMethod): boolean;

  // 基础认证方法
  signUp(email: string, password: string): Promise<AuthResponse>;
  signIn(email: string, password: string): Promise<AuthResponse>;
  signInWithGoogle(): Promise<AuthResponse>;
  signOut(): Promise<AuthResponse>;

  // 邮箱相关
  sendEmailVerification(idToken?: string): Promise<AuthResponse>;
  sendPasswordResetEmail(email: string): Promise<AuthResponse>;
  sendEmailLinkSignIn(email: string): Promise<AuthResponse>;

  // OOB 处理
  handleAuthCallback(mode: string, oobCode: string, continueUrl?: string): Promise<AuthResponse>;
  handlePasswordReset(oobCode: string, newPassword: string): Promise<AuthResponse>;

  // 激活相关
  activateUser(code: string): Promise<AuthResponse>;

  // Token 管理
  idToken(): Promise<string>;

  // 用户管理
  reload(): Promise<AuthResponse>;
  getCurrentUser(): AuthUser | null;

  // 状态检查
  isAuthenticated(): boolean;
  isEmailVerified(): boolean;
  isActivated(): boolean;

  // 策略特定方法
  isAvailable(): Promise<boolean>;
  getHealthStatus(): Promise<{ healthy: boolean; latency?: number }>;
}

// 网络检测服务接口
export interface INetworkDetector {
  checkNetworkStatus(): Promise<NetworkStatus>;
  checkFirebaseAccess(): Promise<boolean>;
  checkGoogleAccess(): Promise<boolean>;
  startMonitoring(callback: (status: NetworkStatus) => void): void;
  stopMonitoring(): void;
  forceRefresh(): Promise<NetworkStatus>;
  setConfig(config: any): void;
}

// 认证策略工厂接口
export interface IAuthStrategyFactory {
  createStrategy(strategy: AuthStrategy): IAuthStrategy;
  getRecommendedStrategy(networkStatus: NetworkStatus): AuthStrategy;
  switchStrategy(newStrategy: AuthStrategy): Promise<void>;
}
