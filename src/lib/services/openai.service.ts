import OpenAI from "openai";
import type { ChatCompletionMessageParam, ReasoningEffort } from "openai/resources.js";
import { authService } from "./auth.service";

export async function createOpenaiClient() {
    const idToken = await authService.idToken();
    return new OpenAI({
        baseURL:
            new URL(window.location.href).origin + "/api/ai/_openai_2233",
        apiKey: idToken,
        dangerouslyAllowBrowser: true,
    });
}

export async function sendLiteOpenaiRequest(signal: AbortSignal, texts: string[], prompt?: string) {
    const openai = await createOpenaiClient();
    const messages: ChatCompletionMessageParam[] = texts.map((item) => {
        return {
            role: "user",
            content: item,
        };
    });
    if (prompt) {
        messages.unshift({
            role: "system",
            content: prompt,
        })
    }
    const response = await openai.chat.completions.create(
        {
            model: "gemini-2.5-flash-lite",
            reasoning_effort: "none" as ReasoningEffort,
            messages,
            temperature: 0.8,
            max_tokens: 300,
        },
        { signal },
    );

    const content = response.choices[0].message.content || "";

    return content;
}