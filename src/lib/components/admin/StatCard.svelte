<script lang="ts">
  import { Card } from "$lib/components/ui";
  import type { ComponentType, SvelteComponent } from "svelte";

  interface Props {
    title: string;
    value: number | string;
    breakdown?: string;
    colorScheme?: "blue" | "green" | "orange" | "red" | "purple";
  }

  let { title, value, breakdown, colorScheme = "blue" }: Props = $props();

  const colorClasses = {
    blue: {
      bg: "bg-gradient-to-r from-blue-500 to-blue-600",
      text: "text-blue-100",
      icon: "text-blue-200",
    },
    green: {
      bg: "bg-gradient-to-r from-green-500 to-green-600",
      text: "text-green-100",
      icon: "text-green-200",
    },
    orange: {
      bg: "bg-gradient-to-r from-orange-500 to-orange-600",
      text: "text-orange-100",
      icon: "text-orange-200",
    },
    red: {
      bg: "bg-gradient-to-r from-red-500 to-red-600",
      text: "text-red-100",
      icon: "text-red-200",
    },
    purple: {
      bg: "bg-gradient-to-r from-purple-500 to-purple-600",
      text: "text-purple-100",
      icon: "text-purple-200",
    },
  };

  const classes = colorClasses[colorScheme];
</script>

<Card class="{classes.bg} text-white border-0">
  <div class="flex items-center justify-between">
    <div>
      <p class="{classes.text} text-sm font-medium">{title}</p>
      <p class="text-3xl font-bold">{value}</p>
      {#if breakdown}
        <p class="{classes.text} text-xs mt-1">
          {breakdown}
        </p>
      {/if}
    </div>
  </div>
</Card>
