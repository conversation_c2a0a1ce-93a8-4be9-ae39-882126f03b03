import { appModelsTestDB } from "$lib/indexeddb/db";
import type { AuthUser } from "$lib/auth/auth-strategy.types";
import { createDatabaseStore } from "$lib/services/db.service";
import { createPersistedStore } from "$lib/services/local.service";

// 激活码
export const activationCode = createPersistedStore<string>("activationCode", "");
// AI 模型
export const aiModel = createPersistedStore<string>("AiModel", "gemini-2.5-flash-lite");
// 用户授权字段
export const userAuth = createPersistedStore("userAuth", {} as AuthUser)

export const appModelsTestChats = createDatabaseStore(appModelsTestDB, "chats", {
    pageSize: 30,
});
