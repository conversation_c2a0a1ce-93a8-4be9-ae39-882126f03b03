import admin from 'firebase-admin';
import { env } from "$env/dynamic/private";
import { ActivationCodeType, QuotaType, type ActivationCode } from '$lib/types/activation-code.types';
import type { UserQuote } from '$lib/auth/auth-strategy.types';
import type { ChatCompletionResponse } from '$lib/services/server.service';
import type { CompletionUsage } from 'openai/resources';

// 配置邮件链接设置
// 身份认证 rest api docs
// https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn
// 邮箱链接认证请求回调地址
// 该页面会请求 https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw
// https://ai-novel-983d1.firebaseapp.com/__/auth/action?apiKey=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw&mode=signIn&oobCode=n2bfvW4wRiyUFHe8MmqturQkvj-rRodujU-wI4A0qwsAAAGYdf5sAw&continueUrl=http://localhost:3004/login/callback&lang=en
export function initializeFirebaseAdmin() {
    const serviceAccount = JSON.parse(
        env.GOOGLE_FIREBASE_ADMIN_SERVICE_ACCOUNT
    );

    if (!admin.apps || !admin.apps.length) {
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount), // Your service account JSON
        });
    }
    return { fireauth: admin.auth(), firedb: admin.firestore() }
}

export async function useQuota(token: string, type: QuotaType, action: () => Promise<ChatCompletionResponse>) {
    const { fireauth, firedb } = initializeFirebaseAdmin();
    const user = await fireauth.verifyIdToken(token);
    const record = await fireauth.getUser(user.uid);
    const quotaPool: UserQuote[] = record.customClaims?.quota_pool
    console.log("get user", quotaPool);

    if (!quotaPool || 0 == quotaPool.length) {
        return new Response(JSON.stringify({ success: false, error: "No quota available (100401)" }), { status: 401 });
    }
    // 未使用的额度
    let unusage = 0;
    quotaPool.forEach(quota => {
        switch (type) {
            case QuotaType.AGENT:
                unusage += quota.amount.agent - quota.used.agent;
                break;
            case QuotaType.COMPLETION:
                unusage += quota.amount.completion - quota.used.completion;
                break;
            default:
                break;
        }
    });
    if (unusage <= 0) {
        return new Response(JSON.stringify({ success: false, error: "No quota available (101401)" }), { status: 401 });
    }
    const response = await action();
    // 优先消耗订阅计划里的额度
    let index = quotaPool.findIndex(quota => quota.type === ActivationCodeType.PLAN && quota.used[type] < quota.amount[type]);
    if (-1 == index) {
        index = quotaPool.findIndex(quota => quota.type === ActivationCodeType.BONUS && quota.used[type] < quota.amount[type]);
    }
    if (0 <= index) {
        quotaPool[index].used[type] += 1;
    }
    fireauth.setCustomUserClaims(user.uid, { quota_pool: quotaPool });

    const usage = response.data?.usage ? response.data.usage : {} as CompletionUsage;

    const now = Date.now();
    const docId = `${user.uid}_${now}`
    const docRef = firedb.collection('user_token_usages').doc(docId);
    // 创建用户 token 使用数据
    const userTokenUsage = {
        uid: user.uid,
        email: user.email || record.email,
        used: 1,
        model: type,
        // 来源
        quotaId: quotaPool[index].id,
        // token 使用情况, 以 openai 为例
        tokenUsage: [usage],
        createdAt: now
    };
    // 保存到数据库
    await docRef.set(userTokenUsage);

    return new Response(JSON.stringify(response.data ? response.data : response.error), {
        status: response.status,
        headers: {
            'Content-Type': 'application/json'
        }
    });
}


export async function validActivationCode(code: string) {
    const db = admin.firestore();
    const collectionRef = db.collection("activation_codes");

    // 获取文档引用
    let docRef = collectionRef.doc(code);
    // 使用 get() 获取文档快照
    try {
        const docSnap = await docRef.get();

        return {
            isValid: docSnap.exists,
            snapshot: docSnap
        }
    } catch (error) {
        console.error("Admin SDK 获取文档时出错：", error);
        return {
            isValid: false,
            snapshot: null
        }
    }
}

export async function validUserToken(email: string, token?: string) {
    if (!token || token.trim().length == 0) return false;
    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken.email ? decodedToken.email === email : false;
}

export async function updateUserActivationCode(email: string, userToken: string, activationCode: string) {
    if (!validUserToken(email, userToken)) {
        return { success: false, err: "Invalid user token" };
    }
    try {
        const user = await admin.auth().getUserByEmail(email);

        const db = admin.firestore();
        const docRef = db.collection('activation_codes').doc(activationCode);
        const doc = await docRef.get();

        if (!doc.exists) {
            return { success: false, err: "Invalid activation code (102)" };
        }

        const data = doc.data() as ActivationCode;
        data.activatedAt = Date.now();
        data.activatedEmail = email;
        data.isActive = true;

        await docRef.update(data as any);

        admin.auth().setCustomUserClaims(user.uid, {
            quota_pool: [
                {
                    id: activationCode,
                    type: ActivationCodeType.BONUS,
                    amount: {
                        agent: data.amount.agent,
                        completion: data.amount.completion
                    },
                    used: {
                        agent: 0,
                        completion: 0
                    },
                    created_at: new Date().toUTCString()
                }
            ],
        });
        return { success: true, err: null };
    } catch (error) {
        console.error("Admin SDK 更新用户激活码时出错：", error);
        return { success: false, err: JSON.stringify(error) };
    }
}