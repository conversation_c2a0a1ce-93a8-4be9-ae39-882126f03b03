/**
 * 激活码管理系统类型定义
 * 
 * 激活码分为两种类型：
 * 1. bonus: 奖励类型，一次性额度，不会重置，用完为止
 * 2. plan: 订阅计划类型，每月重置的额度，会过期
 */

// 激活码类型枚举
export enum ActivationCodeType {
  BONUS = 'bonus',    // 奖励类型
  PLAN = 'plan'       // 订阅计划类型
}

// 激活码状态枚举
export enum ActivationCodeStatus {
  ACTIVE = 'active',      // 可用
  USED = 'used',          // 已使用
  EXPIRED = 'expired'     // 已过期
}

// 额度类型枚举
export enum QuotaType {
  AGENT = 'agent',    // AI 代理
  COMPLETION = 'completion'       // 文本补全
}

// 额度类型定义
export interface QuotaAmount {
  agent: number;        // AI 代理额度
  completion: number;   // 文本补全额度
}

// 激活码接口定义
export interface ActivationCode {
  // 激活代码，最高 20 位，含大小写和数字，可手动填写
  code: string;

  // 激活码的额度
  amount: QuotaAmount;

  // 额度类型，bonus: 奖励，plan: 订阅计划
  type: ActivationCodeType;

  // 是否已激活
  isActive: boolean;

  // 有效时限，单位月，从激活时间开始计算，为 0 表示不限制
  validity: number;

  // 创建时间（时间戳）
  createdAt: number;

  // 过期时间，默认 1 个月未使用则过期，无法使用
  expireAt: number;

  // 激活时间（时间戳，null 表示未激活）
  activatedAt: number | null;

  // 激活邮箱
  activatedEmail: string | null;

  // 激活码备注
  remark: string | null;

  // 是否为非卖品
  notForSale?: boolean;

  // 当前状态（计算属性，基于其他字段计算得出）
  status?: ActivationCodeStatus;

  // 剩余有效天数（计算属性）
  remainingDays?: number;
}

// 激活码创建表单接口
export interface CreateActivationCodeForm {
  // 激活代码（可选，如果不提供则自动生成）
  code?: string;

  // 激活码的额度
  amount: QuotaAmount;

  // 额度类型
  type: ActivationCodeType;

  // 有效时限，单位月
  validity: number;

  // 过期时间
  expireAt: string;

  // 激活码备注
  remark?: string;

  // 是否为非卖品
  notForSale?: boolean;
}

// 激活码更新表单接口
export interface UpdateActivationCodeForm {
  // 激活码ID
  id: string;

  // 激活码的额度（可选）
  amount?: QuotaAmount;

  // 有效时限，单位月（可选）
  validity?: number;

  // 过期时间
  expireAt?: string;

  // 激活码备注（可选）
  remark?: string;

  // 是否为非卖品（可选）
  notForSale?: boolean;
}

// 激活码列表查询参数
export interface ActivationCodeQueryParams {
  // 页码
  page?: number;

  // 每页数量
  limit?: number;

  // 搜索关键词（搜索激活码或备注）
  search?: string;

  // 按类型筛选
  type?: ActivationCodeType;

  // 按状态筛选
  status?: ActivationCodeStatus;

  // 排序字段
  sortBy?: 'createdAt' | 'activatedAt' | 'expireAt' | 'validity';

  // 排序方向
  sortOrder?: 'asc' | 'desc';
}

// 激活码统计信息
export interface ActivationCodeStats {
  // 总激活码数
  total: number;

  // 可用激活码数
  active: number;

  // 已使用激活码数
  used: number;

  // 已过期激活码数
  expired: number;

  // 按类型分组统计
  byType: {
    bonus: {
      total: number;
      active: number;
      used: number;
      expired: number;
    };
    plan: {
      total: number;
      active: number;
      used: number;
      expired: number;
    };
  };

}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页响应接口
export interface PaginatedResponse<T = any> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 激活码列表响应
export type ActivationCodeListResponse = ApiResponse<PaginatedResponse<ActivationCode>>;

// 激活码统计响应
export type ActivationCodeStatsResponse = ApiResponse<ActivationCodeStats>;

// 激活码详情响应
export type ActivationCodeDetailResponse = ApiResponse<ActivationCode>;
